    //******************************************************************** LoginPluginProxy  *****************************************************************

    module.exports = {

        capture: function (successCallback, errorCallback, param) {
            //param[0] = parameters and param[1] is login listener callback
           // CaptureImageWithinBox.CaptureImageWithinBox.setLoginParameters(JSON.stringify(param[0]));
            var ev = new CaptureImageWithinBox.CaptureImageWithinBox();
            ev.capture();
        }      
    };

    require("cordova/exec/proxy").add("CaptureImageWithinBox", module.exports);

    //********************************************************************** DatabasePluginProxy ********************************************************************//

  