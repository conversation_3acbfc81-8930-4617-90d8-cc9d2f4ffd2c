<ion-header>
  <ion-toolbar >
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
      <!-- <ion-button (click)="listAndRemove()" color="primary">
        <ion-icon name="trash" color="primary"></ion-icon>
      </ion-button> -->
    </ion-buttons>
    <ion-title>
      {{'Samson' | translate}}
    </ion-title>
    <i class="fa-sharp fa-solid fa-location-dot"></i>
    <ion-buttons color="primary" slot="end">
      <ion-button color="primary" (click)="presentPopover()">
        <ion-icon name="ellipsis-vertical-sharp" color="primary"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div *ngIf="device.platform !== 'windows' && device.platform !== 'browser'" style="text-align: center !important; width: 100% !important; height: 100% !important">
    <div class="module mid inspection ion-activatable" (click)="helpService.helpMode ? '' : readExternalFile("C:\Users\<USER>\AppData\Roaming\Electron\tempImage")" [style.background-image]="'linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),url(' + dataService.imageurlInspection + ')'" [style.background-size]="'100% 100% !important;'">
      <ion-ripple-effect></ion-ripple-effect>
      <h4>{{'Inspection' | translate}}</h4>
    </div>
    <div class="module mid line ion-activatable" (click)="helpService.helpMode ? '' : lineTracker()" [style.background-image]="'linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),url(' + dataService.imageurlLineManagement + ')'" [style.background-size]="'100% 100% !important;'">
      <ion-ripple-effect></ion-ripple-effect>
      <!-- <h4 style=" font-size: 30px;">{{'LineTracker ™'}}</h4> -->
      <h4 style=" font-size: 30px;">{{'Line Management'}}</h4>
    </div>
    <div class="module mid resource ion-activatable" (click)="helpService.helpMode ? '' : resource()">
      <ion-ripple-effect></ion-ripple-effect>
      <h4>{{'Resource' | translate}}</h4>
    </div>
    <div class="module mid connect ion-activatable" (click)="helpService.helpMode ? '' : contact()">
      <ion-ripple-effect></ion-ripple-effect>
      <h4>{{'Contact us' | translate}}</h4>
    </div>

    <!-- <div class="module mid connect ion-activatable" (click)="helpService.helpMode ? '' : agree()">
        <ion-ripple-effect></ion-ripple-effect>
        <h4>{{'Contact us' | translate}}</h4>
      </div> -->
  </div>

  <div *ngIf="device.platform == 'windows' || device.platform == 'browser'">
    <div class="gridCon"> 
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : inspection()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageUrlInspectionWindows" class="imageTag">
          <p class="wrapperLabel">{{'INSPECTIONS' | translate}}</p>
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : resource()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/op-imgs/Main-Menu_Resources.png" class="imageTag">
          <p class="wrapperLabel">{{'RESOURCES' | translate}}</p>
        </div>
      </div>
      <div class="gridCol">
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : lineTracker()">
          <ion-ripple-effect></ion-ripple-effect>
          <img [src]="dataService.imageurlLineManagementWindows" class="imageTag">
          <p class="wrapperLabel">{{'LINE MANAGEMENT'}}</p>
          <!-- <p class="wrapperLabel">{{'LINETRACKER ™'}}</p> -->
        </div>
        <div class="imgWrapper ion-activatable" (click)="helpService.helpMode ? '' : contact()">
          <ion-ripple-effect></ion-ripple-effect>
          <img src="./assets/img/winImg/op-imgs/Main-Menu_Contact.png" class="imageTag">
          <p class="wrapperLabel">{{'CONTACT US' | translate}}</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>