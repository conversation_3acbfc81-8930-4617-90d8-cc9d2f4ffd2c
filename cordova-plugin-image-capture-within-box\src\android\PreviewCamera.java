package com.unvired.cordova.capture;


import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.core.ImageInfo;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageDecoder;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import com.samson.inspections.R;


public class PreviewCamera extends AppCompatActivity {
    ImageView imageView;
    Button retake, useImg;
        private static final int CAPTURE_IMAGE_REQUEST_CODE = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preview_camera);
        imageView = findViewById(R.id.capturedImageView);
        retake = findViewById(R.id.bRetake);
        useImg = findViewById(R.id.bUseImage);
        CaptureImageWithinBox capture =  new CaptureImageWithinBox();

        Uri myUri = Uri.parse(getIntent().getStringExtra("imageByteArray"));
        imageView.setImageURI(myUri);

        retake.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(PreviewCamera.this,OpenCamera.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
            }
        });
        useImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            
            int width = getIntent().getIntExtra("GuidelineBoxWidth",340);
            int height = getIntent().getIntExtra("GuidelineBoxHeight",256);
            JSONObject imageInfo = new JSONObject();
                try {
                    imageInfo.put("ImageData", myUri);
                    imageInfo.put("GuidelineBoxWidth", width);
                    imageInfo.put("GuidelineBoxHeight", height);
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
              Intent intent = new Intent();
               try {
                intent.putExtra("jsonObject", imageInfo.toString(4));
              } catch (JSONException e) {
                throw new RuntimeException(e);
              }
              setResult(RESULT_OK, intent);
              finish();
              // capture.sendCapturedImage(imageInfo);

            }
        });
    }
    public static String uriToBase64(Context context, Uri uri) {
        InputStream inputStream = null;
        try {
            inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream != null) {
                byte[] bytes = readBytes(inputStream);
                return Base64.encodeToString(bytes, Base64.DEFAULT);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private static byte[] readBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteBuffer = new ByteArrayOutputStream();
        int bufferSize = 1024;
        byte[] buffer = new byte[bufferSize];
        int len;
        while ((len = inputStream.read(buffer)) != -1) {
            byteBuffer.write(buffer, 0, len);
        }
        return byteBuffer.toByteArray();
    }
}
