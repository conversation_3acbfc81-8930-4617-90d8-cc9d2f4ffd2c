@objc(CaptureImageWithinBox) class CaptureImageWithinBox : CDVPlugin, CameraViewControllerDelegate {

    private var currentCallbackId = ""
    
    @objc(capture:) 
    func capture(_ command: CDVInvokedUrlCommand) {
        print("Plugin Called")
        let storyboard = UIStoryboard(name: "<PERSON>View", bundle: nil)
        guard let vc = storyboard.instantiateViewController(withIdentifier: "CameraViewController") as? CameraViewController, let topVC = CaptureImageWithinBox.topMostController() else {
            self.commandDelegate!.send(
            CDVPluginResult(status: CDVCommandStatus_ERROR, messageAs: "Unable to open camera"), callbackId: command.callbackId)
            return
        }
        let sizeInfo = command.arguments[0] as? [String: CGFloat] ?? [:]
        vc.preferredWidth = sizeInfo["RequiredWidthAsPerMLModel"] ?? 200.0
        vc.preferredHeight = sizeInfo["RequiredHeightAsPerMLModel"] ?? 256.0
        vc.cameraDelegate = self
        vc.modalPresentationStyle = .fullScreen
        topVC.present(vc, animated: true)
        self.currentCallbackId = command.callbackId
    }

    func capturedImage(_ data: [String: Any]) {
        let pluginResult = CDVPluginResult(status: CDVCommandStatus_OK, messageAs: data)
        self.commandDelegate!.send(pluginResult, callbackId: currentCallbackId)
    }

    static func topMostController() -> UIViewController? {
        guard let window = UIApplication.shared.keyWindow, let rootViewController = window.rootViewController else {
            return nil
        }
        var topController = rootViewController
        while let newTopController = topController.presentedViewController {
            topController = newTopController
        }
        return topController
    }
}
