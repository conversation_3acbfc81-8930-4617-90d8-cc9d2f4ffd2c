<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <view
        android:id="@+id/previewView"
        class="androidx.camera.view.PreviewView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/linear"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />

    <com.unvired.cordova.capture.RectangleOverlayView
        android:id="@+id/rectangleOverlayView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/linear"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <LinearLayout
        android:id="@+id/orientRopeLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp"
        android:background="@android:color/black"
        app:layout_constraintBottom_toTopOf="@id/linear"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/orientRopeLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Orient Rope to Pattern overlay"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_gravity="center"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/linear"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:orientation="horizontal"
        android:padding="5dp"
        android:background="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/bCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Cancel"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:background="@android:color/transparent"
                android:layout_marginEnd="9dp" />

            <Button
                android:id="@+id/bCapture"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Capture"
                android:textAllCaps="false" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="end"
            android:orientation="horizontal">

            <Button
                android:id="@+id/increaseButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+"
                android:textAllCaps="false" />

            <Button
                android:id="@+id/decreaseButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="-"
                android:textAllCaps="false" />
        </LinearLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>