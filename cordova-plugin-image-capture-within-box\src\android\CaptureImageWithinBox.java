package com.unvired.cordova.capture;

import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.CallbackContext;
import org.apache.cordova.CordovaInterface;
import org.apache.cordova.CordovaWebView;
import org.apache.cordova.PluginResult;
import org.json.JSONObject;


import org.json.JSONArray;
import org.json.JSONException;

import android.content.Intent;
import android.util.Log;



public class CaptureImageWithinBox extends CordovaPlugin {

    private static final int CAPTURE_IMAGE_REQUEST_CODE = 0;

    private CallbackContext callbackContext;

    @Override
    public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException {

      Log.d("CaptureImageWithinBox", "Loading");
      if (action.equals("capture")) {
        this.callbackContext = callbackContext;
        startCaptureActivity();
        return true;
      }

      return false;
    }

    private void startCaptureActivity() {
        Intent captureIntent = new Intent(cordova.getActivity(), OpenCamera.class);
        cordova.startActivityForResult(this, captureIntent, CAPTURE_IMAGE_REQUEST_CODE);
    }

    public void sendCapturedImage(JSONObject imageInfo){
        PluginResult result = new PluginResult(PluginResult.Status.OK, imageInfo);
				result.setKeepCallback(true);
				sendResult(result);
    }

    private void sendResult(PluginResult result) {
		if (this.callbackContext != null) {
			this.callbackContext.sendPluginResult(result);
		}
	}


   @Override
    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        super.onActivityResult(requestCode, resultCode, intent);
        if (requestCode == CAPTURE_IMAGE_REQUEST_CODE) {
            if (resultCode == cordova.getActivity().RESULT_OK) {
                String capturedImageData = intent.getStringExtra("imageInfo");
                this.callbackContext.success(capturedImageData);
            } else if (resultCode == cordova.getActivity().RESULT_CANCELED) {
                //this.callbackContext.error("Capture canceled");
                Log.d("Capture cancelled","Capture cancelled");
            } else {
                this.callbackContext.error("Capture failed");
            }
        }
    }
}
