import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UnviredCordovaSDK, NotificationListenerType, ResultType, RequestType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { <PERSON><PERSON><PERSON>ontroller, AlertController, MenuController } from '@ionic/angular';
import { AppConstant } from 'src/constants/appConstants';
import { GenericListPage } from '../generic-list/generic-list.page';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { UserPreferenceService } from '../services/user-preference.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faHome, faTh, faEnvelope, faTasks, faTimes,faSortDown } from '@fortawesome/free-solid-svg-icons';
@Component({
  selector: 'app-account-setup',
  templateUrl: './account-setup.page.html',
  styleUrls: ['./account-setup.page.scss'],
})
export class AccountSetupPage implements OnInit {

  accountsList: any;
  selectedAccount: any = '';
  isEmpty: boolean = true;
  assetList: any;
  selectedAsset: any = '';
  customizationAlert: any;

  constructor(
    private alertService: AlertService,
    private modalController: ModalController,
    public unviredCordovaSDK: UnviredCordovaSDK,
    public alertController: AlertController,
    public device: Device,
    public userPreferenceService: UserPreferenceService,
    public router: Router,
    public dataService: DataService,
    private menu:MenuController,
    public faIconLibrary : FaIconLibrary) {
      this.faIconLibrary.addIcons(faBars, faHome, faTh, faEnvelope, faTasks,faTimes,faSortDown);  
    }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.menu.enable(false,'menu');
    this.registerlistener();
    this.loadAccountList();
  }

  ionViewWillLeave() {
    this.menu.enable(true,'menu');
  }

  registerlistener() {
    this.unviredCordovaSDK.registerNotifListener().subscribe((result) => {
      this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", JSON.stringify(result));
      console.log("Result account setup " 
      + result.type)
      switch (result.type) {
        case NotificationListenerType.dataReceived:
          // this.dataService.clearRefreshDownloadComplete();
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "DATA RECIVED");
          break;
        case NotificationListenerType.dataChanged:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "DATA CHANGED");
          break;
        case NotificationListenerType.dataSend:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "DATA SENT");
          break;
        case NotificationListenerType.appReset:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "APP RESET");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.attachmentDownloadSuccess:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "attachmentDownloadSuccess");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.attachmentDownloadError:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "attachmentDownloadError");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.incomingDataProcessingFinished:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "incomingDataProcessingFinished");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          // if(this.userPreferenceSet == false) {
          //   this.showUserPreferenceAlert();
          //   this.userPreferenceSet = true
          // }
          // this.showUserPreferenceAlert();
          // this.refreshCalled = false;
          // this.dataService.clearDataRefresh();
          // if(this.customizationAlert) {
          //   this.customizationAlert.dismiss();
          // }
          break;
        case NotificationListenerType.attachmentDownloadWaiting:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "attachmentDownloadWaiting");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.infoMessage:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "infoMessage");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          this.handleInfoMessage(result)

          break;
        case NotificationListenerType.serverError:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "serverError");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        case NotificationListenerType.attachmentDownloadCompleted:
          this.unviredCordovaSDK.logInfo("HOME", "registerNotifListener", "attachmentDownloadCompleted");
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
        default:
          this.dataService.clearDataRefresh();
          if (this.customizationAlert) {
            this.customizationAlert.dismiss();
          }
          break;
      }
    });
  }

  async loadAccountList() {
    await this.getAccountList();
    if(this.selectedAccount != undefined || this.selectedAccount != '' || this.selectedAccount.NAME != '') {
      await this.getAssetsList();
    }
  }

  async getAccountList() {
    let accountsListRes = await this.unviredCordovaSDK.dbExecuteStatement("SELECT * FROM ACCOUNT_HEADER ORDER BY NAME COLLATE NOCASE ASC")
    if (accountsListRes.type == ResultType.success) {
      if (accountsListRes.data.length > 0) {
        this.accountsList = accountsListRes.data;
        if (this.accountsList.length == 1) {
          this.selectedAccount = this.accountsList[0]
        }
      } else {
        this.accountsList = [{ NAME: "", VALUE: "No Data Found" }]
        this.selectedAccount = this.accountsList[0]
      }
    } else {
      this.unviredCordovaSDK.logError("create-inspection", "getAccountHeader", "Error while getting error from db" + JSON.stringify(accountsListRes))
    }
    this.assetList = [{ NAME: '', VALUE: 'No Data Found' }];
    this.selectedAsset = this.assetList[0];
  }


  async presentModal(title) {
    this.alertService.present().then(async () => {
      var tempList, pageTitle;
      switch (title) {
        case 'ACCOUNT':
          tempList = this.accountsList
          pageTitle = "Accounts"
          break;
        case 'ASSET':
          tempList = this.assetList
          pageTitle = "Assets"
          break;

      }

      const modal = await this.modalController.create({
        component: GenericListPage,
        componentProps: { value: tempList, title: pageTitle, page: title }
      });
      await modal.present();
      modal.onDidDismiss().then(async (data) => {
        if(data?.data) {
          switch (title) {
            case 'ACCOUNT':
              this.selectedAccount = data.data.data
              this.isEmpty = true;
              this.selectedAsset = '';
              await this.getAssetsList();
              break;
            case 'ASSET':
              const assetValue = data.data.data.NAME;
              this.selectedAsset = data.data.data
              this.isEmpty = true;
              break;
          }
          // this.preferenceForm.patchValue({
          //   asset: assetValue
          // });
        }
      });
    });
  }

  async getAssetsList() {
    const asset = await this.unviredCordovaSDK.dbSelect('ASSET_HEADER', 'ACNT_ID = "' + this.selectedAccount.ID + '" ORDER BY NAME COLLATE NOCASE ASC');
    if (asset.type === ResultType.success) {
      if (asset.data.length > 0) {
        this.assetList = asset.data;
      } else {
        this.assetList = [{ NAME: '', VALUE: 'No Data Found' }];
        this.selectedAsset = this.assetList[0];
      }
      // this.selectedAsset = this.assetList[0];
    }
  }

  async showCustomizationAlert() {
    var tempUser = await this.unviredCordovaSDK.userSettings()
    console.log(tempUser);
    this.customizationAlert = await this.alertController.create({
      header: 'Alert',
      message: '<div><div class="imageStyle"><img src="./assets/img/blue-hourglass.gif"></div>' + 'Please wait while we configure data. Do not exit this screen.</div>',
      backdropDismiss: false,
      cssClass: 'waitImage',
    });
    await this.customizationAlert.present();
    console.log("Download alert presented")
    this.unviredCordovaSDK.logDebug("home", "ionViewWillEnter", " Download alert presented ")
    if(this.device.platform == "browser") {
      setTimeout(() => {
        this.getCustomization();
      }, 1000)
    } else {
      var temp = await this.unviredCordovaSDK.dbDelete("DWNLD_TIME_HEADER", "")
      setTimeout(() => {
        this.getCustomization();
      }, 1000)
    }
    await this.customizationAlert.onDidDismiss().then(async (data) => {
      console.log("Download alert dismissed")
      this.unviredCordovaSDK.logDebug("home", "ionViewWillEnter", "Download alert dismissed")
      await this.userPreferenceService.dbInsertOrUpdate('accountAssetSet', 'true');
      await this.userPreferenceService.dbInsertOrUpdate('showPreferenceDialogue', 'true');
      await this.userPreferenceService.dbInsertOrUpdate('asset', JSON.stringify(this.selectedAsset));
      await this.userPreferenceService.dbInsertOrUpdate('account', JSON.stringify(this.selectedAccount));
      this.dataService.setLastUsedAccount(this.selectedAccount);
      this.dataService.setLastUsedAsset(this.selectedAsset);
      this.router.navigate(['home'])
      this.dataService.firstLogIn = false;
    })
  }

  getCustomization() {
   
      console.log("making async call")
      var inputJson = {
        "downloadHistoricalInsp":"X",
        "accountId": this.selectedAccount.ID,
        "assetId":this.selectedAsset.ID
    }
      this.unviredCordovaSDK.syncBackground(RequestType.PULL, "", inputJson, AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, "", "", true).then((result) => {
        if (result.type == ResultType.success) {
          console.log("result ++ " + JSON.stringify(result))
          this.unviredCordovaSDK.logDebug("home", "ionViewWillEnter", "result ++ " + JSON.stringify(result))
          this.dataService.refreshCalled = true;
          this.dataService.refreshData();
        }
      })
    
  }

  async getMasterDataForBrowser() {
    var inputJson = { 
      "downloadHistoricalInsp":"X",
      "accountId": this.selectedAccount.ID,
      "assetId":this.selectedAsset.ID
    }
    var temp = await this.unviredCordovaSDK.syncForeground(RequestType.QUERY, "", inputJson, AppConstant.PA_ROPE_INSPECTION_PA_GET_CUSTOMIZATON, true)
        if(temp != undefined) {
          if (temp.type == ResultType.success) {
            if (this.customizationAlert) {
              await this.userPreferenceService.dbInsertOrUpdate('asset', JSON.stringify(this.selectedAsset));
              await this.userPreferenceService.dbInsertOrUpdate('account', JSON.stringify(this.selectedAccount));
              this.dataService.setLastUsedAccount(this.selectedAccount);
              this.dataService.setLastUsedAsset(this.selectedAsset);
              this.customizationAlert.dismiss();
              this.unviredCordovaSDK.dbSaveWebData();
              this.dataService.showSaveDB = true;
            }
          } else {
            this.router.navigate(['browser-home'], {replaceUrl: true})
            this.customizationAlert.dismiss();  
          }
        } else {
          this.router.navigate(['browser-home'], {replaceUrl: true})
          this.customizationAlert.dismiss();         
        }
  }

  public handleInfoMessage(result) {

    this.unviredCordovaSDK.logInfo("Utility", "handleInfoMessage()", "Info Message:" + JSON.stringify(result, null, 2))

    if (!result.data || result.data.length == 0) {
      return
    }

    let messages: any = result.data
    if (!messages || messages.length == 0) {
      return
    }

    let messageTitle: string = messages[0].CATEGORY
    if (!messageTitle) {
      messageTitle = "Information"
    }

    var messageString = ''
    messages.forEach(element => {
      if (element.MESSAGE) {
        messageString += element.MESSAGE + '<br>'
      }
    });

    this.showAlert(messageTitle.substring(0, 1).toUpperCase() + messageTitle.substr(1).toLowerCase(), messageString)
  }

  async showAlert(title, message) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });
    await alert.present();
  }

  clearAsset() {
    if(this.dataService.selectedRole == "Customer") {
      this.selectedAsset = '';
    }
  }

  clearAccount() {
    if(this.dataService.selectedRole == "Customer") {
      this.selectedAccount = '';
    }
  }

}
