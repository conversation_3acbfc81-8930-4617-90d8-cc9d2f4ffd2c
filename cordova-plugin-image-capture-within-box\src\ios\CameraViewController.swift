//
//  ViewController.swift
//  Guided Camera
//
//  Created by zawyenaing on 2018/10/01.
//  Copyright © 2018 swift.test. All rights reserved.
//

import UIKit
import AVFoundation
import Foundation

extension UIImageView {
    func roundCorners(_ corners:UIRectCorner, radius: CGFloat) {
        let path = UIBezierPath(roundedRect: self.bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        self.layer.mask = mask
    }
}

protocol CameraViewControllerDelegate : NSObjectProtocol {
    func capturedImage(_ imageInfo: [String: Any])
}

class CameraViewController: UIViewController, AVCapturePhotoCaptureDelegate {
    
    
    @IBOutlet weak var previewView: UIView!
    @IBOutlet weak var capturedImageView: UIImageView!
    @IBOutlet weak var cameraActionView: UIView!
    @IBOutlet weak var choosePhotoActionView: UIView!
    
    @IBOutlet weak var photoSaveButton: UIButton!
    @IBOutlet weak var photoCancelButton: UIButton!
    @IBOutlet weak var cameraButton: UIButton!
    @IBOutlet weak var stepper: UIStepper!
    
    var croppedImageView = UIImageView()
    var cropImageRect = CGRect()
    var cropImageRectCorner = UIRectCorner()
    
    var captureSession: AVCaptureSession!
    var stillImageOutput: AVCapturePhotoOutput!
    var videoPreviewLayer: AVCaptureVideoPreviewLayer!
    private var videoDeviceInput: AVCaptureDeviceInput!

    weak var cameraDelegate: CameraViewControllerDelegate?

    var preferredWidth: CGFloat = 200.0
    var preferredHeight: CGFloat = 256.0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.navigationController?.navigationBar.barTintColor = #colorLiteral(red: 0, green: 0, blue: 0, alpha: 1)
        self.navigationController?.navigationBar.titleTextAttributes = [NSAttributedString.Key.foregroundColor: #colorLiteral(red: 1, green: 1, blue: 1, alpha: 1)]
        
//        cameraButton.backgroundColor = .white
        cameraButton.layer.borderWidth = 5
        cameraButton.layer.borderColor = UIColor.white.cgColor
        cameraButton.layer.cornerRadius = cameraButton.frame.height/2
        
        stepper.minimumValue = 150
        stepper.maximumValue = view.frame.width
        stepper.value = preferredWidth

        if #available(iOS 13.0, *) {
            stepper.overrideUserInterfaceStyle = .dark
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        
        captureSession = AVCaptureSession()
        captureSession.sessionPreset = .photo
        guard let backCamera = AVCaptureDevice.default(for: AVMediaType.video)
            else {
                print("Unable to access back camera")
                return
        }
        
        do {
            videoDeviceInput = try AVCaptureDeviceInput(device: backCamera)
            stillImageOutput = AVCapturePhotoOutput()
            stillImageOutput.isHighResolutionCaptureEnabled = true
            
            if captureSession.canAddInput(videoDeviceInput) && captureSession.canAddOutput(stillImageOutput) {
                captureSession.addInput(videoDeviceInput)
                captureSession.addOutput(stillImageOutput)
                setupCameraPreview()
                // Add a gesture recognizer to the camera view to detect taps.
//                let tapGestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
//                previewView.addGestureRecognizer(tapGestureRecognizer)
            }
            
            // Set the AVCaptureDevice's focus mode to AVCaptureDevice.FocusMode.continuousAutoFocus.
            if backCamera.isFocusModeSupported(.continuousAutoFocus) {
                try backCamera.lockForConfiguration()
                backCamera.focusMode = .continuousAutoFocus
                backCamera.unlockForConfiguration()
            }
        }
        catch let error  {
            print("Error Unable to initialize back camera:  \(error.localizedDescription)")
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        self.captureSession.stopRunning()
    }
    
    override var shouldAutorotate: Bool {
        return false
    }

    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return UIInterfaceOrientationMask.portrait
    }

    override var preferredInterfaceOrientationForPresentation: UIInterfaceOrientation {
        return UIInterfaceOrientation.portrait
    }
    
//    @objc func handleTapGesture(_ sender: UITapGestureRecognizer) {
//        // Get the point of the tap.
//        let tappedFocusPoint = sender.location(in: previewView)
//        
//                
//        // we need to move the focus point to be the center of the tap instead of (0.0, 0.0)
//        let centerX = tappedFocusPoint.x - (previewView.frame.size.width / 2.0)
//        let centerY = tappedFocusPoint.y - (previewView.frame.size.height / 2.0)
//
//        let focusPoint = CGPoint(x: centerX, y: centerY)
//        
//        // we need to remap the point because of different coordination systems.
//        let convertedFocusPoint = videoPreviewLayer.captureDevicePointConverted(fromLayerPoint: focusPoint)
//
//
//        // Focus the camera at the specified point.
//        do {
//            try videoDeviceInput.device.lockForConfiguration()
//            videoDeviceInput.device.focusPointOfInterest = convertedFocusPoint
//            videoDeviceInput.device.unlockForConfiguration()
//        } catch {
//            print("Error focusing camera: \(error)")
//        }
//    }
    
    func setupCameraPreview() {
        
        let imageView = setupGuideLineArea()
        imageView.tag = 1
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer.videoGravity = .resizeAspectFill
        videoPreviewLayer.connection?.videoOrientation = .portrait
        previewView.layer.addSublayer(videoPreviewLayer)
        previewView.clipsToBounds = true
        previewView.addSubview(imageView)
        
        
        if #available(iOS 13.0, *) {
            let blurEffect = UIBlurEffect(style: .systemUltraThinMaterialDark)
            
            let leftBlurView = UIVisualEffectView(effect: blurEffect)
            leftBlurView.tag = 2
            leftBlurView.frame = CGRect(x: 0, y: 0, width: imageView.frame.minX, height: previewView.frame.height)
            previewView.addSubview(leftBlurView)
            
            let rightBlurView = UIVisualEffectView(effect: blurEffect)
            rightBlurView.tag = 3
            rightBlurView.frame = CGRect(x: imageView.frame.maxX, y: 0, width: previewView.frame.width - imageView.frame.maxX, height: leftBlurView.frame.height)
            previewView.addSubview(rightBlurView)
        } else {
            let leftBlurView = UIView()
            leftBlurView.backgroundColor = .black
            leftBlurView.tag = 2
            leftBlurView.frame = CGRect(x: 0, y: 0, width: imageView.frame.minX, height: previewView.frame.height)
            previewView.addSubview(leftBlurView)
            
            let rightBlurView = UIView()
            rightBlurView.backgroundColor = .black
            rightBlurView.tag = 3
            rightBlurView.frame = CGRect(x: imageView.frame.maxX, y: 0, width: previewView.frame.width - imageView.frame.maxX, height: leftBlurView.frame.height)
            previewView.addSubview(rightBlurView)
        }
        
        
        
//        let topBlurView = UIVisualEffectView(effect: blurEffect)
//        topBlurView.frame = CGRect(x: leftBlurView.frame.maxX, y: 0, width: rightBlurView.frame.minX - leftBlurView.frame.maxX, height: imageView.frame.minY)
//        previewView.addSubview(topBlurView)
//        
//        let bottomBlurView = UIVisualEffectView(effect: blurEffect)
//        bottomBlurView.frame = CGRect(x: topBlurView.frame.minX, y: imageView.frame.maxY, width: topBlurView.frame.width, height: previewView.frame.height - imageView.frame.maxY)
//        previewView.addSubview(bottomBlurView)
        
        cropImageRect = imageView.frame
        
        DispatchQueue.global(qos: .userInitiated).async {
            self.captureSession.startRunning()
            DispatchQueue.main.async {
                self.videoPreviewLayer.frame = self.previewView.bounds
            }
        }
    }
    
    func updateCameraViewFrames() {
        guard let imageView = previewView.viewWithTag(1) as? UIImageView, let leftBlurView = previewView.viewWithTag(2) as? UIVisualEffectView, let rightBlurView = previewView.viewWithTag(3) as? UIVisualEffectView else {
            return
        }
        imageView.frame.size = CGSizeMake(stepper.value, imageView.frame.height)
        imageView.center = CGPoint(x: previewView.bounds.midX, y: previewView.bounds.midY);
        leftBlurView.frame = CGRect(x: 0, y: 0, width: imageView.frame.minX, height: previewView.frame.height)
        rightBlurView.frame = CGRect(x: imageView.frame.maxX, y: 0, width: previewView.frame.width - imageView.frame.maxX, height: leftBlurView.frame.height)
        cropImageRect = imageView.frame
//        self.videoPreviewLayer.frame = imageView.frame
    }
    
    func setupGuideLineArea() -> UIImageView {
        
        // let edgeInsets:UIEdgeInsets = UIEdgeInsets.init(top: 22, left: 22, bottom: 22, right: 22)
        // let resizableImage = (UIImage(named: "guideImage")?.resizableImage(withCapInsets: edgeInsets, resizingMode: .stretch))!

//        let widthRatio: CGFloat = previewView.frame.size.width/preferredWidth
//        let heightRatio: CGFloat = previewView.frame.size.height/preferredHeight
//
//        var imgWidth = previewView.frame.size.width
//        var imgHeight = previewView.frame.size.height
//
//        if preferredWidth > preferredHeight {
//            imgWidth = preferredWidth * widthRatio
//            imgHeight = preferredHeight * widthRatio
//            if imgHeight > previewView.frame.size.height {
//                imgWidth = preferredWidth * heightRatio
//                imgHeight = preferredHeight * heightRatio
//            }
//        }
//        if preferredHeight > preferredWidth {
//            imgWidth = preferredWidth * heightRatio
//            imgHeight = preferredHeight * heightRatio
//            if imgWidth > previewView.frame.size.width {
//                imgWidth = preferredWidth * widthRatio
//                imgHeight = preferredHeight * widthRatio
//            }
//        }
        
        var imgWidth = previewView.frame.size.width/3
        let imgHeight = previewView.frame.size.height
        
        if imgWidth < preferredWidth {
            imgWidth = previewView.frame.size.width/2
        }
        
        if imgWidth < preferredWidth {
            imgWidth = preferredWidth
        }
        
        stepper.value = imgWidth
        
        let imageSize = CGSize(width: imgWidth, height: imgHeight)
        cropImageRectCorner = [.allCorners]
        
        let imageView = UIImageView(image: nil)
        imageView.frame.size = imageSize
        imageView.tintColor = .systemYellow
        imageView.center = CGPoint(x: previewView.bounds.midX, y: previewView.bounds.midY);
        imageView.layer.borderColor = UIColor.systemYellow.cgColor
        imageView.layer.borderWidth = 4
                
        // Setup Child ImageView
        let ropePatternImageView = UIImageView(image: #imageLiteral(resourceName: "<EMAIL>"))
        ropePatternImageView.alpha = 0.2
        ropePatternImageView.contentMode = .scaleToFill
        ropePatternImageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.addSubview(ropePatternImageView)
        
        // Set constraints for child image view (Relative to Parent)
        NSLayoutConstraint.activate([
            ropePatternImageView.centerXAnchor.constraint(equalTo: imageView.centerXAnchor),
            ropePatternImageView.centerYAnchor.constraint(equalTo: imageView.centerYAnchor),
            ropePatternImageView.widthAnchor.constraint(equalTo: imageView.widthAnchor, multiplier: 1),
            ropePatternImageView.heightAnchor.constraint(equalTo: imageView.heightAnchor, multiplier: 1)
        ])
        
        return imageView
    }
    
    func previewViewLayerMode(image: UIImage?, isCameraMode: Bool, originalImg: UIImage? = nil) {
        if isCameraMode {
            DispatchQueue.global(qos: .userInitiated).async {
                self.captureSession.startRunning()
            }
            
            cameraActionView.isHidden = false
            choosePhotoActionView.isHidden = true
            
            previewView.isHidden = false
            capturedImageView.isHidden = true
        } else {
            self.captureSession.stopRunning()
            cameraActionView.isHidden = true
            choosePhotoActionView.isHidden = false
            
            previewView.isHidden = true
            capturedImageView.isHidden = false
            
            if #available(iOS 13.0, *) {
                // Original image to blureffect
                let blurEffect = UIBlurEffect(style: .systemUltraThinMaterialDark)
                let blurView = UIVisualEffectView(effect: blurEffect)
                blurView.frame = capturedImageView.bounds
                capturedImageView.addSubview(blurView)
            }
            else {
                let blurView = UIView()
                blurView.backgroundColor = .black
                blurView.frame = capturedImageView.bounds
                capturedImageView.addSubview(blurView)
            }
            
            // Crop guide Image
            croppedImageView = UIImageView(image: image!)
//            croppedImageView.center = CGPoint(x:capturedImageView.frame.width/2, y:capturedImageView.frame.height/2)
            croppedImageView.frame = cropImageRect
//            croppedImageView.roundCorners(cropImageRectCorner, radius: 0)
//            croppedImageView.layer.borderColor = UIColor.systemYellow.cgColor
//            croppedImageView.layer.borderWidth = 4
            capturedImageView.addSubview(croppedImageView)
            capturedImageView.image = originalImg
            view.backgroundColor = .black
            capturedImageView.backgroundColor = .black
        }
    }
    
    // MARK: - AVCapturePhotoCaptureDelegate
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        
        guard error == nil else {
            print("Fail to capture photo: \(String(describing: error))")
            return
        }
        
        // Check if the pixel buffer could be converted to image data
        guard let imageData = photo.fileDataRepresentation() else {
            print("Fail to convert pixel buffer")
            return
        }
        
        let orgImage : UIImage = UIImage(data: imageData)!
        capturedImageView.image = orgImage
        let originalSize: CGSize
        let visibleLayerFrame = cropImageRect
        
        // Calculate the fractional size that is shown in the preview
        let metaRect = (videoPreviewLayer?.metadataOutputRectConverted(fromLayerRect: visibleLayerFrame )) ?? CGRect.zero
        
        if (orgImage.imageOrientation == UIImage.Orientation.left || orgImage.imageOrientation == UIImage.Orientation.right) {
            originalSize = CGSize(width: orgImage.size.height, height: orgImage.size.width)
        } else {
            originalSize = orgImage.size
        }
        let cropRect: CGRect = CGRect(x: metaRect.origin.x * originalSize.width, y: metaRect.origin.y * originalSize.height, width: metaRect.size.width * originalSize.width, height: metaRect.size.height * originalSize.height).integral
        
        if let finalCgImage = orgImage.cgImage?.cropping(to: cropRect) {
            let image = UIImage(cgImage: finalCgImage, scale: 1.0, orientation: orgImage.imageOrientation)
//            UIImageWriteToSavedPhotosAlbum(image, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
//            let alertController = UIAlertController(title: "Alert", message: "Image capture and saved into photo album.", preferredStyle: .alert)
//            alertController.addAction(UIAlertAction(title: "OK", style: .default))
//            present(alertController, animated: true)
            previewViewLayerMode(image: image, isCameraMode: false, originalImg: orgImage)
        }
    }
    
    
    //    func photoOutput(_ captureOutput: AVCapturePhotoOutput, didFinishProcessingPhoto photoSampleBuffer: CMSampleBuffer?, previewPhoto previewPhotoSampleBuffer: CMSampleBuffer?, resolvedSettings: AVCaptureResolvedPhotoSettings, bracketSettings: AVCaptureBracketedStillImageSettings?, error: Error?) {
    //        if let photoSampleBuffer = photoSampleBuffer {
    //            guard let imageData = AVCapturePhotoOutput.jpegPhotoDataRepresentation(forJPEGSampleBuffer: photoSampleBuffer, previewPhotoSampleBuffer: previewPhotoSampleBuffer)
    //                else { return }
    //
    //
    //        }
    //    }
    
    // MARK: - @IBAction
    
    @IBAction func dismiss(_ sender: AnyObject) {
        dismiss(animated: true)
    }
    
    @IBAction func actionCameraCapture(_ sender: AnyObject) {
        
        // Istance of AVCapturePhotoSettings class
        var photoSettings: AVCapturePhotoSettings
        
        photoSettings = AVCapturePhotoSettings.init(format: [AVVideoCodecKey: AVVideoCodecType.jpeg])
        photoSettings.isAutoStillImageStabilizationEnabled = true
//        photoSettings.flashMode = .auto
        
        // AVCapturePhotoCaptureDelegate
        stillImageOutput.capturePhoto(with: photoSettings, delegate: self)
    }
    
    @IBAction func savePhotoPressed(_ sender: Any) {
        // Call Delegate function
        let imgPath = self.saveImageIntoTempDirectory()
//        let strBase64 = croppedImageView.image!.jpegData(compressionQuality: 1)?.base64EncodedString() ?? ""
        let imageInfo: [String: Any] = ["ImageData": imgPath ?? "", "GuidelineBoxWidth": Int(cropImageRect.size.width), "GuidelineBoxHeight": Int(cropImageRect.size.height)]
        self.cameraDelegate?.capturedImage(imageInfo)
        self.dismiss(animated: true, completion: nil)
        // UIImageWriteToSavedPhotosAlbum(croppedImageView.image!, self, #selector(image(_:didFinishSavingWithError:contextInfo:)), nil)
    }
    
    func saveImageIntoTempDirectory() -> String? {
        let uuid = UUID().uuidString
        let dirPath = NSTemporaryDirectory() as NSString
        let imageFileUrl = URL(fileURLWithPath: dirPath.appendingPathComponent(uuid) as String)
        if let imgData = croppedImageView.image!.jpegData(compressionQuality: 1) {
            do {
                try imgData.write(to: imageFileUrl)
                print("Successfully saved image at path: \(imageFileUrl)")
                return imageFileUrl.absoluteString
            } catch {
                print("Error saving image: \(error)")
            }
        }
        return nil;
    }

    @objc func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            
            let alertController = UIAlertController(title: "Save Error", message: error.localizedDescription, preferredStyle: .alert)
            alertController.addAction(UIAlertAction(title: "OK", style: .default, handler: { (alert: UIAlertAction!) in
                self.previewViewLayerMode(image: nil, isCameraMode: true)
            }))
            present(alertController, animated: true)
        } else {
            let alertController = UIAlertController(title: "Saved", message: "Captured guided image saved successfully.", preferredStyle: .alert)
            alertController.addAction(UIAlertAction(title: "OK", style: .default, handler: { (alert: UIAlertAction!) in
                self.previewViewLayerMode(image: nil, isCameraMode: true)
            }))
            present(alertController, animated: true)
        }
    }
    
    @IBAction func cancelPhotoPressed(_ sender: Any) {
        
        previewViewLayerMode(image: nil, isCameraMode: true)
    }
    
    @IBAction func stepperValueChanged(_ sender: UIStepper) {
        print("Stepper Value Changed: \(sender.value)")
        updateCameraViewFrames()
    }
}

