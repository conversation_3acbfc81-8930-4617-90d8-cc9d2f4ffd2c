package com.nileesha.cameraplugin;

import androidx.appcompat.app.AppCompatActivity;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.widget.ImageView;

public class PreviewImage extends AppCompatActivity {
    ImageView image;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preview_image);
        ImageView imageView = findViewById(R.id.capturedImageView);
        image = findViewById(R.id.capturedImageView);

        byte[] byteArray = getIntent().getByteArrayExtra("imageByteArray");

// Convert the byte array back to a Bitmap
        Bitmap receivedBitmap = BitmapFactory.decodeByteArray(byteArray, 0, byteArray.length);

        if (receivedBitmap != null) {
            // Use the non-null Uri for further processing
            // Example: Display the image using an ImageView

            imageView.setImageBitmap(receivedBitmap);
        } else {
            // Handle the case where the Uri is null
            // You can display an error message or take appropriate action
            imageView.setImageResource(R.drawable.ic_launcher_background);
        }
    }
}