package com.unvired.cordova.capture;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.common.util.concurrent.ListenableFuture;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import com.samson.inspections.R;

public class OpenCamera extends AppCompatActivity {

    private ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
    PreviewView previewView;
    private static final int CAMERA_PERMISSION_REQUEST_CODE = 100;
    private static final int STORAGE_PERMISSION_REQUEST_CODE = 101;
    private static final int CAPTURE_IMAGE_REQUEST_CODE = 0;

    static final int REQUEST_CODE_PREVIEW_ACTIVITY = 1;
    Button bTakePicture,bCancel;
    private ImageCapture imageCapture;
    private RectangleOverlayView rectangleOverlayView;
    Bitmap croppedBitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888);
    ProcessCameraProvider cameraProviderStop;

    private final ActivityResultLauncher<Intent> previewCameraLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                    Intent intent = new Intent();
                    String str =  result.getData().getStringExtra("jsonObject");
                    intent.putExtra("imageInfo",str );
                    setResult(RESULT_OK, intent);
                    finish();
                }
            }
    );
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_opencamera);

        bTakePicture = findViewById(R.id.bCapture);
        bCancel = findViewById(R.id.bCancel);
        previewView = findViewById(R.id.previewView);
        rectangleOverlayView = findViewById(R.id.rectangleOverlayView);
        // Display the cropped image in an ImageView



        // Check if CAMERA permission is granted
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            // Request CAMERA permission
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.CAMERA}, CAMERA_PERMISSION_REQUEST_CODE);
        }


        // Check if WRITE_EXTERNAL_STORAGE permission is granted
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            // Request WRITE_EXTERNAL_STORAGE permission
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.WRITE_EXTERNAL_STORAGE}, STORAGE_PERMISSION_REQUEST_CODE);
        }

        bCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Handle cancellation (e.g., stop the camera preview)
                stopCameraPreview(); // Stop the camera preview
            }
        });
        cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            try {
                // Camera provider is now ready, initialize the camera
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                startCameraX(cameraProvider);
            } catch (ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
        }, getExecutor());
        bTakePicture.setOnClickListener(view -> {
            // Capture the image within the boundaries of the rectangle overlay
            //captureImageWithinRectangle();
            capturePhoto();
            Log.d("Image captured", "Image captured successfully");
        });

        Button increaseButton = findViewById(R.id.increaseButton);
        Button decreaseButton = findViewById(R.id.decreaseButton);

        increaseButton.setOnClickListener(v -> rectangleOverlayView.increaseSize());
        decreaseButton.setOnClickListener(v -> rectangleOverlayView.decreaseSize());
    }

    private void stopCameraPreview() {
        // Stop the camera preview (custom logic here if needed)
        super.onBackPressed();

    }

    private Executor getExecutor() {
        return ContextCompat.getMainExecutor(this);
    }

    @SuppressLint("RestrictedApi")
    private void startCameraX(ProcessCameraProvider cameraProvider) {


        Preview preview = new Preview.Builder().build();

        preview.setSurfaceProvider(previewView.getSurfaceProvider());

        imageCapture = new ImageCapture.Builder().build();
        CameraSelector cameraSelector = new CameraSelector.Builder()
                .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                .build();

        cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
    }

    private void capturePhoto() {
        File outputDirectory = getOutputDirectory();
        String fileName = "IMG_" + System.currentTimeMillis() + ".jpg";
        File outputFile = new File(outputDirectory, fileName);

        ImageCapture.OutputFileOptions outputFileOptions =
                new ImageCapture.OutputFileOptions.Builder(outputFile).build();

        imageCapture.takePicture(outputFileOptions, ContextCompat.getMainExecutor(this), new ImageCapture.OnImageSavedCallback() {
            @Override
            public void onImageSaved(ImageCapture.OutputFileResults outputFileResults) {
                Uri savedUri = Uri.fromFile(outputFile);
                runOnUiThread(() -> {
                    try {
                        // Convert URI to Bitmap
//                        Bitmap capturedBitmap = MediaStore.Images.Media.getBitmap(getContentResolver(), savedUri);
                        Bitmap capturedBitmap = loadBitmapFromUri(savedUri);

                        Bitmap croppedImage = rectangleOverlayView.cropSavedImage(capturedBitmap);

                        //This could be due to orientation. May be captured in landscape
                        if(null == croppedImage) {
                            Toast.makeText(OpenCamera.this, "Error capturing image", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        // Save cropped image
                        File croppedFile = saveCroppedImage(croppedImage);

                        // Display or use the cropped image
                        displayCapturedImage(Uri.fromFile(croppedFile));

                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                });
            }

            @Override
            public void onError(ImageCaptureException exception) {
                Toast.makeText(OpenCamera.this, "Error capturing image: " + exception.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private File getOutputDirectory() {
        File mediaDir = new File(getCacheDir(),"Inspection");
        if (!mediaDir.exists() && !mediaDir.mkdirs()) {
            Log.e("Inspections", "Failed to create directory");
        }
        return mediaDir;
    }

    private File saveCroppedImage(Bitmap croppedBitmap) throws IOException {
        File outputDirectory = getOutputDirectory();
        String fileName = "IMG_" + System.currentTimeMillis() + ".jpg";
        File croppedFile = new File(outputDirectory, fileName);

        try (FileOutputStream out = new FileOutputStream(croppedFile)) {
            if(null != croppedBitmap) {
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, out);
            } else {
                Toast.makeText(OpenCamera.this, "Error capturing the image" , Toast.LENGTH_SHORT).show();
            }
        }
        return croppedFile;
    }

    private Bitmap loadBitmapFromUri(Uri uri) {
        try {
            // Step 1: Get image dimensions without loading the full image
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            InputStream inputStream = getContentResolver().openInputStream(uri);
            BitmapFactory.decodeStream(inputStream, null, options);
            inputStream.close();

            // Step 2: Calculate sample size (reduce resolution)
            options.inSampleSize = calculateInSampleSize(options, 1080, 1920); // Adjust based on device
            options.inJustDecodeBounds = false;

            // Step 3: Load scaled-down bitmap
            inputStream = getContentResolver().openInputStream(uri);
            Bitmap bitmap = BitmapFactory.decodeStream(inputStream, null, options);
            inputStream.close();

            // Step 4: Fix orientation and return optimized bitmap
            return rotateBitmapIfNeeded(bitmap, uri);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
    private int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        int height = options.outHeight;
        int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            int halfHeight = height / 2;
            int halfWidth = width / 2;

            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        return inSampleSize;
    }

    private void displayCapturedImage(Uri imageUri) throws IOException {
        Intent intent = new Intent(OpenCamera.this,PreviewCamera.class);
        intent.putExtra("imageByteArray",imageUri.toString());
        intent.putExtra("GuidelineBoxWidth",rectangleOverlayView.getRectWidth());
        intent.putExtra("GuidelineBoxHeight",rectangleOverlayView.getRectHeight());
        intent.putExtra("imageInfo", getIntent().getStringExtra("imageInfo"));
        previewCameraLauncher.launch(intent);
        Toast.makeText(this, "Image captured successfully", Toast.LENGTH_SHORT).show();
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (cameraProviderFuture != null) {

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                cameraProviderFuture.addListener(() -> {
                    try {
                        ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                        cameraProvider.unbindAll();
                    } catch (ExecutionException | InterruptedException e) {
                        e.printStackTrace();
                    }
                }, getMainExecutor());
            }

        }
    }
    private Bitmap rotateBitmapIfNeeded(Bitmap bitmap, Uri imageUri) {
        try {
            InputStream input = getContentResolver().openInputStream(imageUri);
            ExifInterface exif = new ExifInterface(input);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            input.close();

            int rotation = 0;
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    rotation = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    rotation = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    rotation = 270;
                    break;
            }

            if (rotation == 0) return bitmap; // No rotation needed

            // Apply rotation
            Matrix matrix = new Matrix();
            matrix.postRotate(rotation);
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (IOException e) {
            e.printStackTrace();
            return bitmap;
        }
    }
}
