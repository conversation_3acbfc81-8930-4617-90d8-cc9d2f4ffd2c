package com.unvired.cordova.capture;


import android.content.Context;
import android.util.AttributeSet;
import android.graphics.*;
import android.view.View;
import com.samson.inspections.R;

public class RectangleOverlayView extends View {
    private Paint paint, blurPaint, patternPaint;
    private int rectWidth, rectHeight;
    private static final int STEP = 50;
    private Bitmap patternBitmap, blurredBitmap;
    private Canvas blurCanvas;

    public RectangleOverlayView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        // Paint for rectangle border
        paint = new Paint();
        paint.setColor(Color.YELLOW);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(3);

        // Paint for blurred background
        blurPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        blurPaint.setColor(Color.BLACK);
        blurPaint.setAlpha(180);
        blurPaint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.CLEAR));

        // Load the custom pattern bitmap
        patternBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.rope_pattern);

        // Create a shader for seamless tiling
        if (patternBitmap != null) {
            BitmapShader shader = new BitmapShader(patternBitmap, Shader.TileMode.REPEAT, Shader.TileMode.REPEAT);
            patternPaint = new Paint();
            patternPaint.setShader(shader);
            patternPaint.setAlpha(100);
        } else {
            patternPaint = new Paint();
        }

        rectWidth = 200; // Default width
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int screenWidth = getWidth();
        int screenHeight = getHeight();
        if (screenWidth == 0 || screenHeight == 0) return;

        rectHeight = screenHeight; // Full height of the screen

        int left = (screenWidth - rectWidth) / 2;
        int top = 0;
        int right = left + rectWidth;
        int bottom = screenHeight;

        // Create blurred background
        if (blurredBitmap == null || blurredBitmap.getWidth() != screenWidth || blurredBitmap.getHeight() != screenHeight) {
            blurredBitmap = Bitmap.createBitmap(screenWidth, screenHeight, Bitmap.Config.ARGB_8888);
            blurCanvas = new Canvas(blurredBitmap);
        }

        // Draw semi-transparent overlay
        blurCanvas.drawColor(Color.BLACK);
        blurCanvas.drawRect(left, top, right, bottom, blurPaint);

        // Draw blurred overlay onto the main canvas
        canvas.drawBitmap(blurredBitmap, 0, 0, null);

        // Clip canvas to restrict pattern inside rectangle
        canvas.save();
        canvas.clipRect(left, top, right, bottom);

        // Dynamically resize pattern image
        if (patternBitmap != null) {
            Bitmap scaledPattern = Bitmap.createScaledBitmap(patternBitmap, rectWidth, rectHeight, true);
            canvas.drawBitmap(scaledPattern, left, top, patternPaint);
        }

        canvas.restore();

        // Draw yellow rectangle border after clipping reset
        canvas.drawRect(left, top, right, bottom, paint);
    }


    public void increaseSize() {
        if (rectWidth + STEP < getWidth()) {
            rectWidth += STEP;
            invalidate(); // Redraw the view to update the pattern size
        }
    }

    public void decreaseSize() {
        if (rectWidth - STEP > 100) {
            rectWidth -= STEP;
            invalidate(); // Redraw the view to update the pattern size
        }
    }


    public int getRectWidth() {
        return rectWidth;
    }

    public int getRectHeight() {
        return rectHeight;
    }

    public Bitmap cropSavedImage(Bitmap originalBitmap) {
        int originalWidth = originalBitmap.getWidth();
        int originalHeight = originalBitmap.getHeight();

        // Convert overlay rectangle to image coordinates
        float scaleX = (float) originalWidth / getWidth();
        float scaleY = (float) originalHeight / getHeight();

        int cropX = Math.round((getLeft() + (getWidth() - getRectWidth()) / 2) * scaleX);
        int cropY = Math.round(getTop() * scaleY);
        int cropWidth = Math.round(getRectWidth() * scaleX);
        int cropHeight = Math.round(getRectHeight() * scaleY);

        // Ensure crop bounds are within image dimensions
        cropX = Math.max(0, Math.min(cropX, originalWidth - 1));
        cropY = Math.max(0, Math.min(cropY, originalHeight - 1));
        cropWidth = Math.max(1, Math.min(cropWidth, originalWidth - cropX));
        cropHeight = Math.max(1, Math.min(cropHeight, originalHeight - cropY));

        return Bitmap.createBitmap(originalBitmap, cropX, cropY, cropWidth, cropHeight);
    }
}
