import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController, AlertController, Platform, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { AlertService } from '../services/alert.service';
import { DataService } from '../services/data.service';
import { HelpService } from '../services/help.service';
import { UtilserviceService } from '../services/utilservice.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { faBars, faEnvelope, faListCheck, faGrip } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-abrasion-comparator',
  templateUrl: './abrasion-comparator.page.html',
  styleUrls: ['./abrasion-comparator.page.scss'],
})
export class AbrasionComparatorPage implements OnInit {

  constructor(
    public helpService: HelpService,
    public translate: TranslateService,
    public device: Device,
    public router: Router,
    private menu: MenuController, 
    private service: UtilserviceService,
    public alertService: AlertService, 
    public alertController: AlertController, 
    public platform: Platform, 
    public dataService: DataService,
    public toastController: ToastController,
    public faIconLibrary : FaIconLibrary) {

    this.faIconLibrary.addIcons(faBars, faEnvelope, faListCheck, faGrip) 
  }

  ngOnInit() {
  }

  externalAbrasion() {
    this.router.navigate(['abrasion-comparator-external'])
  }

  internalAbrasion() {
    this.router.navigate(['abrasion-comparator-internal'])
  }

  tenexExternalAbrasion() {
    this.router.navigate(['abrasion-comparator-tenex-external']);
  }

  ionViewDidLeave() {
    this.helpService.exitHelpMode();
  }

  back() {
    this.router.navigate(['home']);
  }

  openMenu() {
    this.menu.toggle('menu');
  }

  goToLineTracker() {
    this.dataService.navigateToLineTracker(this)
  }
}
