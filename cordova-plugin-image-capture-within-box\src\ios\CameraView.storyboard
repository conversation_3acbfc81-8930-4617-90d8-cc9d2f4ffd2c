<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Guided Camera-->
        <scene sceneID="BYl-Wf-qgr">
            <objects>
                <viewController storyboardIdentifier="CameraViewController" id="Gsy-6B-TpD" customClass="CameraViewController" customModule="CameraView" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="RFP-YP-YXO">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ev9-gy-cXc">
                                <rect key="frame" x="0.0" y="59" width="393" height="634"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bea-KY-6Pu">
                                <rect key="frame" x="0.0" y="59" width="393" height="634"/>
                            </view>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8qb-6E-cU7" userLabel="PhotoActionView">
                                <rect key="frame" x="0.0" y="693" width="393" height="125"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sg7-83-w6n">
                                        <rect key="frame" x="28" y="57" width="70" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="ZmD-e2-yKO"/>
                                            <constraint firstAttribute="width" constant="70" id="q6x-GE-4Yt"/>
                                        </constraints>
                                        <state key="normal" title="Retake"/>
                                        <connections>
                                            <action selector="cancelPhotoPressed:" destination="Gsy-6B-TpD" eventType="touchUpInside" id="VhG-iK-bNh"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UnW-qr-BRl">
                                        <rect key="frame" x="281" y="57" width="84" height="40"/>
                                        <state key="normal" title="Use Photo"/>
                                        <connections>
                                            <action selector="savePhotoPressed:" destination="Gsy-6B-TpD" eventType="touchUpInside" id="ElA-ze-BTW"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="bottomMargin" secondItem="sg7-83-w6n" secondAttribute="bottom" constant="20" id="5JG-ZS-nYZ"/>
                                    <constraint firstItem="UnW-qr-BRl" firstAttribute="bottom" secondItem="sg7-83-w6n" secondAttribute="bottom" id="CWf-FG-YCJ"/>
                                    <constraint firstAttribute="height" constant="125" id="Gp2-xB-JGj"/>
                                    <constraint firstItem="sg7-83-w6n" firstAttribute="leading" secondItem="8qb-6E-cU7" secondAttribute="leadingMargin" constant="20" id="TUc-A9-3IT"/>
                                    <constraint firstItem="UnW-qr-BRl" firstAttribute="top" secondItem="sg7-83-w6n" secondAttribute="top" id="ghi-X3-RJ6"/>
                                    <constraint firstAttribute="trailingMargin" secondItem="UnW-qr-BRl" secondAttribute="trailing" constant="20" id="uHg-Cp-U2z"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TLP-YX-bq7" userLabel="CameraActionView">
                                <rect key="frame" x="0.0" y="693" width="393" height="125"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Idh-NT-2Qd">
                                        <rect key="frame" x="18" y="52.666666666666629" width="77" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="3ST-c3-S9s"/>
                                        </constraints>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title="Cancel"/>
                                        <connections>
                                            <action selector="dismiss:" destination="Gsy-6B-TpD" eventType="touchUpInside" id="Pgm-qP-rZ2"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="qtn-0H-dRv">
                                        <rect key="frame" x="170.66666666666666" y="46.666666666666629" width="52" height="52"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="52" id="1sX-gh-Lqt"/>
                                            <constraint firstAttribute="height" constant="52" id="Ed6-Gw-A7P"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="26"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="way-Qu-vni">
                                        <rect key="frame" x="164" y="40" width="65" height="65"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="65" id="Sp3-2a-QMd"/>
                                            <constraint firstAttribute="width" constant="65" id="Zdp-mw-vp8"/>
                                        </constraints>
                                        <state key="normal" image="cameraButton"/>
                                        <connections>
                                            <action selector="actionCameraCapture:" destination="Gsy-6B-TpD" eventType="touchUpInside" id="aHp-VD-Ga2"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Orient Rope to Pattern Overlay" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cji-sT-wjo">
                                        <rect key="frame" x="18" y="0.0" width="365" height="40"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stepper opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" maximumValue="100" stepValue="20" translatesAutoresizingMaskIntoConstraints="NO" id="uMJ-7f-95h">
                                        <rect key="frame" x="271" y="56.666666666666629" width="94" height="32"/>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <action selector="stepperValueChanged:" destination="Gsy-6B-TpD" eventType="valueChanged" id="i47-iI-xm7"/>
                                        </connections>
                                    </stepper>
                                </subviews>
                                <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="Cji-sT-wjo" secondAttribute="trailing" constant="10" id="1dn-7y-cKU"/>
                                    <constraint firstItem="way-Qu-vni" firstAttribute="centerX" secondItem="TLP-YX-bq7" secondAttribute="centerX" id="8nN-HT-Hqv"/>
                                    <constraint firstItem="Idh-NT-2Qd" firstAttribute="leading" secondItem="Cji-sT-wjo" secondAttribute="leading" id="9MT-vK-Hmz"/>
                                    <constraint firstItem="Idh-NT-2Qd" firstAttribute="centerY" secondItem="way-Qu-vni" secondAttribute="centerY" id="Ams-K5-jgZ"/>
                                    <constraint firstItem="Cji-sT-wjo" firstAttribute="top" secondItem="TLP-YX-bq7" secondAttribute="top" id="ETY-B4-MjK"/>
                                    <constraint firstAttribute="trailingMargin" secondItem="uMJ-7f-95h" secondAttribute="trailing" constant="20" id="IsC-er-BYH"/>
                                    <constraint firstItem="way-Qu-vni" firstAttribute="centerY" secondItem="qtn-0H-dRv" secondAttribute="centerY" id="IwE-xe-uKJ"/>
                                    <constraint firstAttribute="bottom" secondItem="way-Qu-vni" secondAttribute="bottom" constant="20" id="agR-ej-SGy"/>
                                    <constraint firstItem="way-Qu-vni" firstAttribute="centerX" secondItem="qtn-0H-dRv" secondAttribute="centerX" id="j0c-DP-S9a"/>
                                    <constraint firstAttribute="height" constant="125" id="j32-mi-Jfv"/>
                                    <constraint firstItem="way-Qu-vni" firstAttribute="top" secondItem="Cji-sT-wjo" secondAttribute="bottom" id="kbo-vd-RIm"/>
                                    <constraint firstItem="uMJ-7f-95h" firstAttribute="centerY" secondItem="way-Qu-vni" secondAttribute="centerY" id="t9g-Tq-IM2"/>
                                    <constraint firstItem="Idh-NT-2Qd" firstAttribute="leading" secondItem="TLP-YX-bq7" secondAttribute="leadingMargin" constant="10" id="x2U-Ec-8lj"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6bx-Cs-zU3"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="8qb-6E-cU7" firstAttribute="top" secondItem="TLP-YX-bq7" secondAttribute="top" id="64B-HI-pU1"/>
                            <constraint firstItem="bea-KY-6Pu" firstAttribute="leading" secondItem="6bx-Cs-zU3" secondAttribute="leading" id="FgD-Ki-W7V"/>
                            <constraint firstItem="ev9-gy-cXc" firstAttribute="leading" secondItem="6bx-Cs-zU3" secondAttribute="leading" id="L9J-wW-on7"/>
                            <constraint firstItem="bea-KY-6Pu" firstAttribute="trailing" secondItem="6bx-Cs-zU3" secondAttribute="trailing" id="R0J-bb-nXX"/>
                            <constraint firstItem="bea-KY-6Pu" firstAttribute="top" secondItem="6bx-Cs-zU3" secondAttribute="top" id="TVv-vJ-cmP"/>
                            <constraint firstItem="TLP-YX-bq7" firstAttribute="bottom" secondItem="6bx-Cs-zU3" secondAttribute="bottom" id="U8g-PU-6Zz"/>
                            <constraint firstItem="8qb-6E-cU7" firstAttribute="trailing" secondItem="6bx-Cs-zU3" secondAttribute="trailing" id="UF1-FG-TcO"/>
                            <constraint firstItem="TLP-YX-bq7" firstAttribute="top" secondItem="bea-KY-6Pu" secondAttribute="bottom" id="ZDQ-hM-fH5"/>
                            <constraint firstItem="TLP-YX-bq7" firstAttribute="trailing" secondItem="6bx-Cs-zU3" secondAttribute="trailing" id="Zob-ur-6Wq"/>
                            <constraint firstItem="8qb-6E-cU7" firstAttribute="bottom" secondItem="6bx-Cs-zU3" secondAttribute="bottom" id="eYE-wq-Neo"/>
                            <constraint firstItem="8qb-6E-cU7" firstAttribute="top" secondItem="ev9-gy-cXc" secondAttribute="bottom" id="hOF-F0-xlk"/>
                            <constraint firstItem="8qb-6E-cU7" firstAttribute="leading" secondItem="6bx-Cs-zU3" secondAttribute="leading" id="lGQ-3D-qZH"/>
                            <constraint firstItem="ev9-gy-cXc" firstAttribute="trailing" secondItem="6bx-Cs-zU3" secondAttribute="trailing" id="r5p-5k-kZn"/>
                            <constraint firstItem="ev9-gy-cXc" firstAttribute="top" secondItem="6bx-Cs-zU3" secondAttribute="top" id="tbO-km-tOg"/>
                            <constraint firstItem="TLP-YX-bq7" firstAttribute="leading" secondItem="6bx-Cs-zU3" secondAttribute="leading" id="wPT-a4-Q6t"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" title="Guided Camera" id="DJx-Gw-zSt"/>
                    <connections>
                        <outlet property="cameraActionView" destination="TLP-YX-bq7" id="jnG-TQ-5uY"/>
                        <outlet property="cameraButton" destination="way-Qu-vni" id="UHB-XO-2Ln"/>
                        <outlet property="capturedImageView" destination="ev9-gy-cXc" id="DOn-cD-E4d"/>
                        <outlet property="choosePhotoActionView" destination="8qb-6E-cU7" id="Cqr-1C-EdR"/>
                        <outlet property="photoCancelButton" destination="sg7-83-w6n" id="f5S-yM-5rT"/>
                        <outlet property="photoSaveButton" destination="UnW-qr-BRl" id="zK4-ak-jmN"/>
                        <outlet property="previewView" destination="bea-KY-6Pu" id="SgM-vF-rcs"/>
                        <outlet property="stepper" destination="uMJ-7f-95h" id="ahr-eK-5jq"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="WfE-Lx-pph" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3.8167938931297707" y="8.4507042253521139"/>
        </scene>
    </scenes>
    <resources>
        <image name="cameraButton" width="65" height="65"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
