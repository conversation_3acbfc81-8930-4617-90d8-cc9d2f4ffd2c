<?xml version='1.0' encoding='utf-8'?>
<plugin id="cordova-plugin-image-capture-within-box" version="0.0.30"
	xmlns="http://apache.org/cordova/ns/plugins/1.0"
	xmlns:android="http://schemas.android.com/apk/res/android">
	<name>CaptureImageWithinBox</name>
	<js-module name="CaptureImageWithinBox" src="www/CaptureImageWithinBox.js">
		<clobbers target="CaptureImageWithinBox" />
	</js-module>
	<platform name="ios">
		<config-file parent="/*" target="config.xml">
			<feature name="CaptureImageWithinBox">
				<param name="ios-package" value="CaptureImageWithinBox" />
			</feature>
		</config-file>
		<!-- <edit-config target="NSCameraUsageDescription" file="*-Info.plist" mode="merge" overwrite="true">
          <string>Camera permission is required to capture image.</string>
     	</edit-config> -->
		<source-file src="src/ios/CaptureImageWithinBox.swift" />
		<source-file src="src/ios/CameraViewController.swift" />
		<resource-file src="src/ios/CameraView.storyboard" />
    <resource-file src="src/ios/<EMAIL>" />
	</platform>
	 <!-- windows -->
  <platform name="windows">
    <js-module src="src/windows/CaptureProxy.js" name="CaptureProxy">
      <runs />
    </js-module>
    <!-- <framework src="src/windows/CaptureImageWithinBox.Winmd" custom="true"/>    -->
  </platform>
  <platform name="android">
    
        <config-file target="res/xml/config.xml" parent="/*">
            <feature name="CaptureImageWithinBox">
                <param name="android-package" value="com.unvired.cordova.capture.CaptureImageWithinBox"/>
            </feature>
             
        </config-file>
    <config-file target="AndroidManifest.xml" parent="/manifest">
      <uses-permission android:name="android.permission.CAMERA"/>
      <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
      <!-- <uses-feature android:name="android.hardware.camera" android:required="true"/> -->
       <!-- <uses-sdk android:minSdkVersion="22" android:targetSdkVersion="31" /> -->
    </config-file>
    <config-file target="AndroidManifest.xml" parent="application">
      <activity android:name="com.unvired.cordova.capture.OpenCamera" android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
      <activity android:name="com.unvired.cordova.capture.PreviewCamera" android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
    </config-file>

      <!-- //  <framework src="src/android/build-extras.gradle" custom="true" type="gradleReference" /> -->
<source-file src="src/android/CaptureImageWithinBox.java" target-dir="com/unvired/cordova/capture" />
  <source-file src="src/android/OpenCamera.java" target-dir="com/unvired/cordova/capture" />
  <source-file src="src/android/PreviewCamera.java" target-dir="com/unvired/cordova/capture" />
   <source-file src="src/android/RectangleOverlayView.java" target-dir="com/unvired/cordova/capture" />
   <resource-file src="src/android/res/layout/activity_opencamera.xml" target="res/layout/activity_opencamera.xml" />
   <resource-file src="src/android/res/layout/activity_preview_camera.xml" target="res/layout/activity_preview_camera.xml" />
   <resource-file src="src/android/res/drawable/rope_pattern.png" target="res/drawable/rope_pattern.png" />
    <framework src = 'androidx.appcompat:appcompat:1.4.2'/>
    <framework src = 'androidx.constraintlayout:constraintlayout:2.1.4'/>
    <framework src = 'androidx.camera:camera-core:1.1.0-alpha05'/>
    <framework src = 'androidx.camera:camera-camera2:1.1.0-alpha05'/>
    <framework src = 'androidx.camera:camera-lifecycle:1.1.0-alpha05'/>
    <framework src = 'androidx.camera:camera-view:1.0.0-alpha25'/>
   </platform>

</plugin>