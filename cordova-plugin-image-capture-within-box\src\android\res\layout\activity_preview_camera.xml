<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".PreviewCamera">

    <ImageView
        android:id="@+id/capturedImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/linearlayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        />
    <LinearLayout
        android:id="@+id/linearlayout"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:orientation="horizontal"
        android:weightSum="2"
        android:padding="5dp"
        android:background="@color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/bRetake"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Retake"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:layout_gravity="center"
            android:background="@android:color/transparent"
            android:layout_marginEnd="9dp"
            />
        <Button
            android:id="@+id/bUseImage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Use Image"
            android:background="@android:color/transparent"
            android:textColor="@color/white"
            android:textAllCaps="false"
            android:layout_gravity="center"
            />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>