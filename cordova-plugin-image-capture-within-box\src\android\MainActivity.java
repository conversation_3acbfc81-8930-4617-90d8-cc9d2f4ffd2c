package com.nileesha.cameraplugin;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageCapture;
import androidx.camera.core.ImageCaptureException;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.ContentValues;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.ImageView;
import android.widget.Toast;

import com.google.common.util.concurrent.ListenableFuture;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

public class MainActivity extends AppCompatActivity {
    private ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
    PreviewView previewView;
    private static final int CAMERA_PERMISSION_REQUEST_CODE = 100;
    private static final int STORAGE_PERMISSION_REQUEST_CODE = 101;
    ImageView bTakePicture;
    private ImageCapture imageCapture;
    private RectangleOverlayView rectangleOverlayView;
    Bitmap croppedBitmap = Bitmap.createBitmap(1, 1, Bitmap.Config.ARGB_8888);

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        bTakePicture = findViewById(R.id.bCapture);
        previewView = findViewById(R.id.previewView);
        rectangleOverlayView = findViewById(R.id.rectangleOverlayView);
        // Display the cropped image in an ImageView



        // Check if CAMERA permission is granted
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            // Request CAMERA permission
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.CAMERA}, CAMERA_PERMISSION_REQUEST_CODE);
        }


        // Check if WRITE_EXTERNAL_STORAGE permission is granted
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            // Request WRITE_EXTERNAL_STORAGE permission
            ActivityCompat.requestPermissions(this, new String[]{android.Manifest.permission.WRITE_EXTERNAL_STORAGE}, STORAGE_PERMISSION_REQUEST_CODE);
        }

        cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        cameraProviderFuture.addListener(() -> {
            try {
                // Camera provider is now ready, initialize the camera
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                startCameraX(cameraProvider);
            } catch (ExecutionException | InterruptedException e) {
                e.printStackTrace();
            }
        }, getExecutor());
        bTakePicture.setOnClickListener(view -> {
            // Capture the image within the boundaries of the rectangle overlay
            //captureImageWithinRectangle();
            capturePhoto();
            Log.d("Image captured", "Image captured successfully");
        });

    }

    private Executor getExecutor() {
        return ContextCompat.getMainExecutor(this);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        RectangleOverlayView overlayView = findViewById(R.id.rectangleOverlayView);

        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // Update rectangle coordinates and size for landscape orientation
            // overlayView.updateCoordinatesAndSize(100, 100, 100, 100);
        } else {
            // Update rectangle coordinates and size for portrait orientation
            // overlayView.updateCoordinatesAndSize(500, 500, 300, 200);
        }
    }

    @SuppressLint("RestrictedApi")
    private void startCameraX(ProcessCameraProvider cameraProvider) {


        Preview preview = new Preview.Builder().build();

        preview.setSurfaceProvider(previewView.getSurfaceProvider());

        imageCapture = new ImageCapture.Builder().build();
        CameraSelector cameraSelector = new CameraSelector.Builder()
                .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                .build();

        cameraProvider.bindToLifecycle(this, cameraSelector, preview, imageCapture);
    }

    private void capturePhoto() {
        long timeStamp = System.currentTimeMillis();
        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, timeStamp);
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg");

        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
        imageCapture.takePicture(
                new ImageCapture.OutputFileOptions.Builder(
                        getContentResolver(),
                        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                        contentValues
                ).build(),
                getExecutor(),
                new ImageCapture.OnImageSavedCallback() {
                    @Override
                    public void onImageSaved(@NonNull ImageCapture.OutputFileResults outputFileResults) {
                        android.net.Uri imageUri = outputFileResults.getSavedUri();

                        // Crop the captured image to the area within the yellow rectangle
                        croppedBitmap = cropImageToRectangle(imageUri);
                        saveCroppedImage(croppedBitmap);
                        float blurRadius = 10.0f; // Adjust the blur intensity as needed
                        //Bitmap blurredBitmap = applyGaussianBlurToRest(croppedBitmap, blurRadius);
                        ByteArrayOutputStream stream = new ByteArrayOutputStream();
                        croppedBitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
                        byte[] byteArray = stream.toByteArray();
                        // Display the blurred image
                        if(imageUri!=null){
                            displayCapturedImage(byteArray);
                        }
                        else{
                            Toast.makeText(MainActivity.this, "Image save failed", Toast.LENGTH_SHORT).show();
                        }
                       // croppedImageView.setImageBitmap(blurredBitmap);

                        // Save, display, or process the cropped image as needed
                        //saveCroppedImage(croppedBitmap);
                        // Display a message or perform any other actions
                        Toast.makeText(MainActivity.this, "Image captured and cropped", Toast.LENGTH_SHORT).show();
                    }


                    @Override
                    public void onError(@NonNull ImageCaptureException exception) {
                        Toast.makeText(MainActivity.this,"Error: "+exception.getMessage(),Toast.LENGTH_SHORT).show();

                        String displayName = String.valueOf(timeStamp);
                        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, displayName);
                    }
                });

        } else {
            // Request camera permission if not granted
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.CAMERA}, CAMERA_PERMISSION_REQUEST_CODE);
        }
    }

    private ImageCapture.OutputFileOptions getImageCaptureOptions() {

        File outputDirectory = getOutputDirectory();

        // Create a file to store the captured image with a unique name.
        String fileName = "IMG_" + System.currentTimeMillis() + ".jpg";
        File outputFile = new File(outputDirectory, fileName);

        // Define the ContentValues object.
        ContentValues contentValues = new ContentValues();

        // Set the display name and MIME type in ContentValues.
        contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, fileName);
        contentValues.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");

        // Insert the image details into the MediaStore and get the content URI.
       // Uri imageUri = getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);

        // Build the OutputFileOptions using the content URI.
        ImageCapture.OutputFileOptions outputFileOptions = new ImageCapture.OutputFileOptions.Builder(outputFile)
                .build();

        return outputFileOptions;
    }
    private File getOutputDirectory() {
        File mediaDir = new File(getExternalFilesDir(Environment.DIRECTORY_PICTURES), "CameraApp");

        if (!mediaDir.exists() && !mediaDir.mkdirs()) {
            Log.e("CameraApp", "Failed to create directory");
        }

        return mediaDir;
    }
    private void displayCapturedImage(byte[] imageUri) {
        // Load and display the captured image in an ImageView
      //  croppedImageView.setImageURI(imageUri);
        Intent intent = new Intent(MainActivity.this,PreviewImage.class);
        intent.putExtra("imageByteArray",imageUri);
        startActivity(intent);
        Toast.makeText(this, "Image captured successfully", Toast.LENGTH_SHORT).show();
    }

    private void handleImageCaptureError(ImageCaptureException exception) {
        // Handle image capture error
        Toast.makeText(this, "Error capturing image: " + exception.getMessage(), Toast.LENGTH_SHORT).show();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (cameraProviderFuture != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                cameraProviderFuture.addListener(() -> {
                    try {
                        ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                        cameraProvider.unbindAll();
                    } catch (ExecutionException | InterruptedException e) {
                        e.printStackTrace();
                    }
                }, getMainExecutor());
            }
        }
    }

    private Bitmap cropImageToRectangle(android.net.Uri imageUri) {
        try {
            // Load the captured image from the URI
            Bitmap fullImage = MediaStore.Images.Media.getBitmap(getContentResolver(), imageUri);

            int[] location = new int[2];
            rectangleOverlayView.getLocationOnScreen(location);
            // Calculate the rectangle's coordinates and dimensions
            int left =location[0]; // Left coordinate of the rectangle
            int top = location[1]; // Top coordinate of the rectangle
            int right = left + rectangleOverlayView.getRectWidth(); // Right coordinate of the rectangle
            int bottom = top + rectangleOverlayView.getRectHeight(); // Bottom coordinate of the rectangle

            // Crop the image to the area within the rectangle
            Bitmap croppedBitmap = Bitmap.createBitmap(fullImage, left, top, right - left, bottom - top);

            return croppedBitmap;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private void saveCroppedImage(Bitmap croppedBitmap) {
        if (croppedBitmap != null) {
            try {
                String timeStamp = String.valueOf(System.currentTimeMillis());
                String imageFileName = "CroppedImage_" + timeStamp + ".jpg";

                // Define the directory where you want to save the image
                File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);

                // Create a File object for the image
                File imageFile = new File(storageDir, imageFileName);

                // Create an OutputStream to write the bitmap data to the file
                OutputStream outputStream = new FileOutputStream(imageFile);

                // Compress and save the bitmap to the file
                croppedBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);

                // Close the OutputStream
                outputStream.close();

                // Notify the MediaScanner to scan the saved image file
                MediaScannerConnection.scanFile(
                        this,
                        new String[]{imageFile.getAbsolutePath()},
                        null,
                        (path, uri) -> {
                            // Image saved and scanned
                        }
                );
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
